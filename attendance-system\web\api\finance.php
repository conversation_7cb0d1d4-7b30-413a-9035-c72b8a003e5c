<?php
/**
 * Finance API - Handle Payment Management and Exam <PERSON>es
 * Attendance System
 */

require_once 'config.php';

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'list':
        getFinanceData($pdo);
        break;
    case 'student':
        getStudentFinance($pdo, $input['student_id'] ?? '');
        break;
    case 'update_payment':
        updateStudentPayment($pdo, $input);
        break;
    case 'update_threshold':
        updateGlobalThreshold($pdo, $input['threshold'] ?? '');
        break;
    case 'mark_all':
        markAllPayments($pdo, $input['status'] ?? '');
        break;
    case 'payment_history':
        getPaymentHistory($pdo, $input['student_id'] ?? '');
        break;
    case 'eligibility_report':
        getEligibilityReport($pdo);
        break;
    case 'stats':
        getFinanceStats($pdo);
        break;
    case 'bulk_update':
        bulkUpdatePayments($pdo, $input);
        break;
    case 'export_report':
        exportPaymentReport($pdo);
        break;
    default:
        sendResponse(['success' => false, 'message' => 'Invalid action'], 400);
}

/**
 * Get all students' finance data
 */
function getFinanceData($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name as student_name,
                s.email,
                s.course,
                s.status,
                f.required_amount,
                f.amount_paid,
                f.last_payment_date,
                f.payment_status,
                f.notes,
                (f.required_amount - f.amount_paid) as balance,
                CASE 
                    WHEN f.amount_paid >= f.required_amount THEN 'eligible'
                    ELSE 'ineligible'
                END as eligibility_status
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        $stmt->execute();
        $finance = $stmt->fetchAll();
        
        // Calculate summary statistics
        $stats = [
            'total_students' => count($finance),
            'paid_students' => 0,
            'unpaid_students' => 0,
            'partial_students' => 0,
            'total_required' => 0,
            'total_collected' => 0,
            'total_outstanding' => 0
        ];
        
        foreach ($finance as $record) {
            $stats['total_required'] += $record['required_amount'];
            $stats['total_collected'] += $record['amount_paid'];
            $stats['total_outstanding'] += max(0, $record['balance']);
            
            switch ($record['payment_status']) {
                case 'paid':
                    $stats['paid_students']++;
                    break;
                case 'partial':
                    $stats['partial_students']++;
                    break;
                case 'unpaid':
                    $stats['unpaid_students']++;
                    break;
            }
        }
        
       sendResponse([
    'success' => true,
    'data' => $finance,       // ← To this
    'stats' => $stats
]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching finance data: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get finance data for specific student
 */
function getStudentFinance($pdo, $student_id) {
    try {
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                f.required_amount,
                f.amount_paid,
                f.last_payment_date,
                f.payment_status,
                f.notes,
                (f.required_amount - f.amount_paid) as balance
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.student_id = ?
        ");
        $stmt->execute([$student_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse(['success' => false, 'message' => 'Student not found'], 404);
        }
        
        sendResponse([
            'success' => true,
            'student' => $student
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching student finance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update student payment amount
 */
function updateStudentPayment($pdo, $data) {
    try {
        $required = ['student_id', 'amount_paid'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendResponse([
                'success' => false,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ], 400);
        }
        
        $student_id = $data['student_id'];
        $amount_paid = floatval($data['amount_paid']);
        $notes = $data['notes'] ?? null;
        
        if ($amount_paid < 0) {
            sendResponse(['success' => false, 'message' => 'Amount cannot be negative'], 400);
        }
        
        // Get current finance record
        $stmt = $pdo->prepare("
            SELECT required_amount, amount_paid 
            FROM finance 
            WHERE student_id = ?
        ");
        $stmt->execute([$student_id]);
        $current = $stmt->fetch();
        
        if (!$current) {
            sendResponse(['success' => false, 'message' => 'Finance record not found'], 404);
        }
        
        $required_amount = $current['required_amount'];
        
        // Determine payment status
        if ($amount_paid >= $required_amount) {
            $payment_status = 'paid';
        } elseif ($amount_paid > 0) {
            $payment_status = 'partial';
        } else {
            $payment_status = 'unpaid';
        }
        
        // Update finance record
        $stmt = $pdo->prepare("
            UPDATE finance 
            SET amount_paid = ?, 
                payment_status = ?, 
                last_payment_date = CURDATE(),
                notes = ?,
                updated_at = CURRENT_TIMESTAMP 
            WHERE student_id = ?
        ");
        $stmt->execute([$amount_paid, $payment_status, $notes, $student_id]);
        
        logSystemEvent($pdo, 'payment_update', $student_id, [
            'previous_amount' => $current['amount_paid'],
            'new_amount' => $amount_paid,
            'status' => $payment_status,
            'notes' => $notes
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Payment updated successfully',
            'student_id' => $student_id,
            'amount_paid' => $amount_paid,
            'payment_status' => $payment_status,
            'balance' => max(0, $required_amount - $amount_paid)
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error updating payment: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update global exam fee threshold for all students
 */
function updateGlobalThreshold($pdo, $threshold) {
    try {
        if (empty($threshold) || !is_numeric($threshold)) {
            sendResponse(['success' => false, 'message' => 'Valid threshold amount required'], 400);
        }
        
        $threshold = floatval($threshold);
        
        if ($threshold < 0) {
            sendResponse(['success' => false, 'message' => 'Threshold cannot be negative'], 400);
        }
        
        $pdo->beginTransaction();
        
        // Update all existing finance records
        $stmt = $pdo->prepare("
            UPDATE finance 
            SET required_amount = ?,
                payment_status = CASE 
                    WHEN amount_paid >= ? THEN 'paid'
                    WHEN amount_paid > 0 THEN 'partial'
                    ELSE 'unpaid'
                END,
                updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$threshold, $threshold]);
        $affected = $stmt->rowCount();
        
        // Update system setting
        updateSetting($pdo, 'global_exam_threshold', $threshold);
        
        $pdo->commit();
        
        logSystemEvent($pdo, 'threshold_update', null, [
            'new_threshold' => $threshold,
            'affected_students' => $affected
        ]);
        
        sendResponse([
            'success' => true,
            'message' => "Threshold updated for $affected students",
            'new_threshold' => $threshold,
            'affected_students' => $affected
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        sendResponse([
            'success' => false,
            'message' => 'Error updating threshold: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Mark all students as paid or unpaid
 */
function markAllPayments($pdo, $status) {
    try {
        if (!in_array($status, ['paid', 'unpaid'])) {
            sendResponse(['success' => false, 'message' => 'Invalid status'], 400);
        }
        
        if ($status === 'paid') {
            // Mark all as paid (set amount_paid = required_amount)
            $stmt = $pdo->prepare("
                UPDATE finance 
                SET amount_paid = required_amount,
                    payment_status = 'paid',
                    last_payment_date = CURDATE(),
                    updated_at = CURRENT_TIMESTAMP
            ");
        } else {
            // Mark all as unpaid
            $stmt = $pdo->prepare("
                UPDATE finance 
                SET amount_paid = 0,
                    payment_status = 'unpaid',
                    last_payment_date = NULL,
                    updated_at = CURRENT_TIMESTAMP
            ");
        }
        
        $stmt->execute();
        $affected = $stmt->rowCount();
        
        logSystemEvent($pdo, 'bulk_payment_update', null, [
            'status' => $status,
            'affected_students' => $affected
        ]);
        
        sendResponse([
            'success' => true,
            'message' => "Marked $affected students as $status",
            'status' => $status,
            'affected_students' => $affected
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error updating payments: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get payment history for a student
 */
function getPaymentHistory($pdo, $student_id) {
    try {
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        // Get system logs related to payments for this student
        $stmt = $pdo->prepare("
            SELECT 
                event_type,
                event_data,
                created_at
            FROM system_logs 
            WHERE student_id = ? 
            AND event_type IN ('payment_update', 'registration')
            ORDER BY created_at DESC
        ");
        $stmt->execute([$student_id]);
        $logs = $stmt->fetchAll();
        
        $history = [];
        foreach ($logs as $log) {
            $event_data = json_decode($log['event_data'], true);
            $history[] = [
                'date' => $log['created_at'],
                'type' => $log['event_type'],
                'data' => $event_data
            ];
        }
        
        sendResponse([
            'success' => true,
            'student_id' => $student_id,
            'history' => $history
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching payment history: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get eligibility report for exam access
 */
function getEligibilityReport($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                f.required_amount,
                f.amount_paid,
                f.payment_status,
                COALESCE(att.absent_count, 0) as absent_count,
                CASE 
                    WHEN f.amount_paid >= f.required_amount THEN 'eligible'
                    ELSE 'ineligible'
                END as finance_eligible,
                CASE 
                    WHEN COALESCE(att.absent_count, 0) < 4 THEN 'eligible'
                    ELSE 'ineligible'
                END as attendance_eligible,
                CASE 
                    WHEN f.amount_paid >= f.required_amount 
                    AND COALESCE(att.absent_count, 0) < 4 THEN 'eligible'
                    ELSE 'ineligible'
                END as overall_eligible
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            LEFT JOIN (
                SELECT 
                    student_id,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
                FROM attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY student_id
            ) att ON s.student_id = att.student_id
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        $stmt->execute();
        $students = $stmt->fetchAll();
        
        // Calculate summary
        $summary = [
            'total_students' => count($students),
            'finance_eligible' => 0,
            'attendance_eligible' => 0,
            'overall_eligible' => 0,
            'finance_ineligible' => 0,
            'attendance_ineligible' => 0,
            'overall_ineligible' => 0
        ];
        
        foreach ($students as $student) {
            if ($student['finance_eligible'] === 'eligible') {
                $summary['finance_eligible']++;
            } else {
                $summary['finance_ineligible']++;
            }
            
            if ($student['attendance_eligible'] === 'eligible') {
                $summary['attendance_eligible']++;
            } else {
                $summary['attendance_ineligible']++;
            }
            
            if ($student['overall_eligible'] === 'eligible') {
                $summary['overall_eligible']++;
            } else {
                $summary['overall_ineligible']++;
            }
        }
        
        sendResponse([
            'success' => true,
            'students' => $students,
            'summary' => $summary
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error generating eligibility report: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get finance statistics
 */
function getFinanceStats($pdo) {
    try {
        // Overall statistics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_students,
                SUM(required_amount) as total_required,
                SUM(amount_paid) as total_collected,
                SUM(required_amount - amount_paid) as total_outstanding,
                AVG(amount_paid) as avg_paid,
                SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN payment_status = 'partial' THEN 1 ELSE 0 END) as partial_count,
                SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_count
            FROM finance f
            JOIN students s ON f.student_id = s.student_id
            WHERE s.status = 'active'
        ");
        $stmt->execute();
        $overall = $stmt->fetch();
        
        // Payment status by course
        $stmt = $pdo->prepare("
            SELECT 
                s.course,
                COUNT(*) as total_students,
                SUM(f.amount_paid) as total_collected,
                SUM(f.required_amount) as total_required,
                SUM(CASE WHEN f.payment_status = 'paid' THEN 1 ELSE 0 END) as paid_count
            FROM finance f
            JOIN students s ON f.student_id = s.student_id
            WHERE s.status = 'active'
            GROUP BY s.course
            ORDER BY s.course
        ");
        $stmt->execute();
        $by_course = $stmt->fetchAll();
        
        // Recent payments (last 30 days)
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as recent_payments,
                SUM(amount_paid) as recent_amount
            FROM finance
            WHERE last_payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        $recent = $stmt->fetch();
        
        sendResponse([
            'success' => true,
            'overall' => $overall,
            'by_course' => $by_course,
            'recent' => $recent,
            'generated_at' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching finance stats: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Bulk update payments from CSV or form data
 */
function bulkUpdatePayments($pdo, $data) {
    try {
        $updates = $data['updates'] ?? [];
        
        if (empty($updates) || !is_array($updates)) {
            sendResponse(['success' => false, 'message' => 'Updates array required'], 400);
        }
        
        $pdo->beginTransaction();
        
        $updated = 0;
        $errors = [];
        
        foreach ($updates as $update) {
            try {
                if (!isset($update['student_id']) || !isset($update['amount_paid'])) {
                    $errors[] = "Missing student_id or amount_paid in update";
                    continue;
                }
                
                $student_id = $update['student_id'];
                $amount_paid = floatval($update['amount_paid']);
                
                // Get required amount
                $stmt = $pdo->prepare("SELECT required_amount FROM finance WHERE student_id = ?");
                $stmt->execute([$student_id]);
                $finance = $stmt->fetch();
                
                if (!$finance) {
                    $errors[] = "Student ID $student_id not found";
                    continue;
                }
                
                $required_amount = $finance['required_amount'];
                
                // Determine status
                if ($amount_paid >= $required_amount) {
                    $payment_status = 'paid';
                } elseif ($amount_paid > 0) {
                    $payment_status = 'partial';
                } else {
                    $payment_status = 'unpaid';
                }
                
                // Update record
                $stmt = $pdo->prepare("
                    UPDATE finance 
                    SET amount_paid = ?, 
                        payment_status = ?, 
                        last_payment_date = CURDATE(),
                        updated_at = CURRENT_TIMESTAMP 
                    WHERE student_id = ?
                ");
                $stmt->execute([$amount_paid, $payment_status, $student_id]);
                
                $updated++;
                
            } catch (Exception $e) {
                $errors[] = "Error updating student ID {$update['student_id']}: " . $e->getMessage();
            }
        }
        
        if (count($errors) > 0 && $updated === 0) {
            $pdo->rollBack();
            sendResponse([
                'success' => false,
                'message' => 'Bulk update failed',
                'errors' => $errors
            ], 400);
        } else {
            $pdo->commit();
            
            logSystemEvent($pdo, 'bulk_payment_update', null, [
                'updated_count' => $updated,
                'error_count' => count($errors)
            ]);
            
            sendResponse([
                'success' => true,
                'message' => "Successfully updated $updated payments",
                'updated_count' => $updated,
                'errors' => $errors
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        sendResponse([
            'success' => false,
            'message' => 'Error in bulk update: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Export payment report as CSV
 */
function exportPaymentReport($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                s.name,
                s.email,
                s.course,
                f.required_amount,
                f.amount_paid,
                (f.required_amount - f.amount_paid) as balance,
                f.payment_status,
                f.last_payment_date
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        $stmt->execute();
        $data = $stmt->fetchAll();
        
        // For prototype, return data as JSON
        // In production, you might generate actual CSV file
        sendResponse([
            'success' => true,
            'format' => 'csv',
            'filename' => 'payment_report_' . date('Y-m-d') . '.csv',
            'data' => $data,
            'headers' => [
                'name' => 'Student Name',
                'email' => 'Email',
                'course' => 'Course',
                'required_amount' => 'Required Amount',
                'amount_paid' => 'Amount Paid',
                'balance' => 'Balance',
                'payment_status' => 'Payment Status',
                'last_payment_date' => 'Last Payment Date'
            ]
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error exporting report: ' . $e->getMessage()
        ], 500);
    }
}
?>