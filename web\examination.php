<?php
$pageTitle = 'Examination Management';
require_once __DIR__ . '/includes/header.php';

// Get examination statistics
try {
    $db = getDB();
    
    // Get exam eligible students (attendance >= 80% and payments up to date)
    $stmt = $db->query("
        SELECT COUNT(*) as eligible 
        FROM students s 
        LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) 
        WHERE s.status = 'active' 
        AND s.attendance_percentage >= 80 
        AND p.id IS NOT NULL
    ");
    $eligibleStudents = $stmt->fetch()['eligible'];
    
    // Get ineligible students
    $stmt = $db->query("
        SELECT COUNT(*) as ineligible 
        FROM students s 
        LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) 
        WHERE s.status = 'active' 
        AND (s.attendance_percentage < 80 OR p.id IS NULL)
    ");
    $ineligibleStudents = $stmt->fetch()['ineligible'];
    
    // Get total students
    $stmt = $db->query("SELECT COUNT(*) as total FROM students WHERE status = 'active'");
    $totalStudents = $stmt->fetch()['total'];
    
    // Get current exam session status
    $stmt = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'exam_session_active'");
    $examSessionActive = $stmt->fetch()['setting_value'] ?? '0';
    
    // Get exam details
    $stmt = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'current_exam_course'");
    $currentExamCourse = $stmt->fetch()['setting_value'] ?? '';
    
    $stmt = $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'current_exam_date'");
    $currentExamDate = $stmt->fetch()['setting_value'] ?? '';
    
    // Get exam attendance records
    $stmt = $db->query("
        SELECT s.name, s.student_id, s.attendance_percentage, 
               CASE WHEN p.id IS NOT NULL THEN 'Paid' ELSE 'Pending' END as payment_status,
               CASE WHEN s.attendance_percentage >= 80 AND p.id IS NOT NULL THEN 'Eligible' ELSE 'Ineligible' END as exam_status
        FROM students s 
        LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) 
        WHERE s.status = 'active'
        ORDER BY s.name
    ");
    $examEligibilityList = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Examination page error: " . $e->getMessage());
    $eligibleStudents = $ineligibleStudents = $totalStudents = 0;
    $examSessionActive = '0';
    $currentExamCourse = $currentExamDate = '';
    $examEligibilityList = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-graduation-cap text-primary me-2"></i>
                        Examination Management
                    </h1>
                    <p class="text-muted mb-0">Control exam access based on attendance and payment eligibility</p>
                </div>
            </div>

            <!-- Exam Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-check text-success fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Eligible</div>
                                    <div class="fs-4 fw-bold text-success"><?php echo $eligibleStudents; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-times text-danger fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Ineligible</div>
                                    <div class="fs-4 fw-bold text-danger"><?php echo $ineligibleStudents; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-users text-primary fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Total Students</div>
                                    <div class="fs-4 fw-bold text-primary"><?php echo $totalStudents; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-percentage text-warning fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Eligibility Rate</div>
                                    <div class="fs-4 fw-bold text-warning">
                                        <?php echo $totalStudents > 0 ? round(($eligibleStudents / $totalStudents) * 100) : 0; ?>%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Exam Control Panel -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cog me-2"></i>
                                Exam Setup
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="exam-setup-form">
                                <div class="mb-3">
                                    <label for="course-name" class="form-label">Course Name</label>
                                    <input type="text" class="form-control" id="course-name" 
                                           value="<?php echo htmlspecialchars($currentExamCourse); ?>" 
                                           placeholder="Enter course name">
                                </div>
                                <div class="mb-3">
                                    <label for="exam-date" class="form-label">Exam Date</label>
                                    <input type="date" class="form-control" id="exam-date" 
                                           value="<?php echo $currentExamDate; ?>">
                                </div>
                                <div class="mb-3">
                                    <label for="exam-time" class="form-label">Exam Time</label>
                                    <input type="time" class="form-control" id="exam-time">
                                </div>
                                <button type="button" class="btn btn-primary" onclick="setupExam()">
                                    <i class="fas fa-save me-2"></i>
                                    Setup Exam
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-play-circle me-2"></i>
                                Exam Session Control
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>Session Status:</span>
                                    <span class="badge <?php echo $examSessionActive == '1' ? 'bg-success' : 'bg-secondary'; ?>">
                                        <?php echo $examSessionActive == '1' ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </div>
                            <?php if ($currentExamCourse): ?>
                            <div class="mb-3">
                                <small class="text-muted">Current Exam:</small>
                                <div class="fw-bold"><?php echo htmlspecialchars($currentExamCourse); ?></div>
                                <?php if ($currentExamDate): ?>
                                <div class="text-muted small"><?php echo date('F j, Y', strtotime($currentExamDate)); ?></div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                            <div class="d-grid gap-2">
                                <?php if ($examSessionActive == '1'): ?>
                                <button class="btn btn-danger" onclick="stopExamSession()">
                                    <i class="fas fa-stop me-2"></i>
                                    Stop Exam Session
                                </button>
                                <?php else: ?>
                                <button class="btn btn-success" onclick="startExamSession()" 
                                        <?php echo !$currentExamCourse ? 'disabled' : ''; ?>>
                                    <i class="fas fa-play me-2"></i>
                                    Start Exam Session
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Exam Eligibility List -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Student Exam Eligibility
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Student ID</th>
                                    <th>Name</th>
                                    <th>Attendance</th>
                                    <th>Payment Status</th>
                                    <th>Exam Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($examEligibilityList as $student): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                    <td>
                                        <span class="badge <?php echo $student['attendance_percentage'] >= 80 ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $student['attendance_percentage']; ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $student['payment_status'] == 'Paid' ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo $student['payment_status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $student['exam_status'] == 'Eligible' ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $student['exam_status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewStudentDetails('<?php echo $student['student_id']; ?>')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Preserve all original JavaScript functionality from examination.html
async function setupExam() {
    const courseName = document.getElementById('course-name').value;
    const examDate = document.getElementById('exam-date').value;
    const examTime = document.getElementById('exam-time').value;
    
    if (!courseName || !examDate) {
        alert('Please fill in course name and exam date');
        return;
    }
    
    try {
        const response = await fetch('api/exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'setup',
                course_name: courseName,
                exam_date: examDate,
                exam_time: examTime
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Exam setup completed successfully');
            location.reload();
        } else {
            alert(data.message || 'Failed to setup exam');
        }
    } catch (error) {
        console.error('Error setting up exam:', error);
        alert('Failed to setup exam');
    }
}

async function startExamSession() {
    try {
        const response = await fetch('api/exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'start_session'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Exam session started successfully');
            location.reload();
        } else {
            alert(data.message || 'Failed to start exam session');
        }
    } catch (error) {
        console.error('Error starting exam session:', error);
        alert('Failed to start exam session');
    }
}

async function stopExamSession() {
    if (!confirm('Are you sure you want to stop the exam session?')) return;
    
    try {
        const response = await fetch('api/exam.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'stop_session'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Exam session stopped successfully');
            location.reload();
        } else {
            alert(data.message || 'Failed to stop exam session');
        }
    } catch (error) {
        console.error('Error stopping exam session:', error);
        alert('Failed to stop exam session');
    }
}

function viewStudentDetails(studentId) {
    // Redirect to student details page
    window.location.href = `students/index.php?id=${studentId}`;
}
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
