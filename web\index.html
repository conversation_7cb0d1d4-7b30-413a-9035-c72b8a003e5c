<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #000000 0%, #8b5cf6 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #000000 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #000000 0%, #8b5cf6 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.25);
        }
        
        .loading {
            display: none;
            margin-top: 2rem;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-fingerprint"></i>
        </div>
        <h1>Advanced Attendance System</h1>
        <p>Professional RFID & Fingerprint Management</p>
        
        <a href="dashboard.php" class="btn" id="enter-btn">
            <i class="fas fa-sign-in-alt"></i>
            Enter System
        </a>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="margin-top: 1rem;">Loading system...</p>
        </div>
    </div>

    <script>
        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const savedUser = localStorage.getItem('attendance_user');
            if (savedUser) {
                // User is logged in, redirect to dashboard
                showLoading();
                setTimeout(() => {
                    window.location.href = 'dashboard.php';
                }, 1000);
            }
        });

        // Handle enter button click
        document.getElementById('enter-btn').addEventListener('click', function(e) {
            e.preventDefault();
            showLoading();
            
            // Check if user is logged in
            const savedUser = localStorage.getItem('attendance_user');
            if (savedUser) {
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.php';
                }, 1000);
            } else {
                // Redirect to login
                setTimeout(() => {
                    window.location.href = 'auth/login.php';
                }, 1000);
            }
        });

        function showLoading() {
            document.getElementById('enter-btn').style.display = 'none';
            document.getElementById('loading').style.display = 'block';
        }
    </script>
</body>
</html>
