<?php
/**
 * Students API - Handle Student Registration and Management
 * Attendance System
 */

require_once 'config.php';

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'register':
        registerStudent($pdo, $input);
        break;
    case 'list':
        getStudents($pdo);
        break;
    case 'get':
        getStudent($pdo, $input['student_id'] ?? '');
        break;
    case 'update':
        updateStudent($pdo, $input);
        break;
    case 'delete':
        deleteStudent($pdo, $input['student_id'] ?? '');
        break;
    case 'search':
        searchStudents($pdo, $input['query'] ?? '');
        break;
    case 'get_by_rfid':
        getStudentByRFID($pdo, $input['rfid_id'] ?? '');
        break;
    case 'get_by_fingerprint':
        getStudentByFingerprint($pdo, $input['fingerprint_id'] ?? '');
        break;
    default:
        sendResponse(['success' => false, 'message' => 'Invalid action'], 400);
}

/**
 * Register new student with RFID and fingerprint data
 */
function registerStudent($pdo, $data) {
    try {
        // Validate required fields
        $required = ['name', 'email', 'course', 'rfid_id'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendResponse([
                'success' => false,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ], 400);
        }
        
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT student_id FROM students WHERE email = ?");
        $stmt->execute([$data['email']]);
        if ($stmt->fetch()) {
            sendResponse([
                'success' => false,
                'message' => 'Email already exists'
            ], 409);
        }
        
        // Check if RFID already exists
        $stmt = $pdo->prepare("SELECT student_id FROM students WHERE rfid_id = ?");
        $stmt->execute([$data['rfid_id']]);
        if ($stmt->fetch()) {
            sendResponse([
                'success' => false,
                'message' => 'RFID card already registered'
            ], 409);
        }
        
        $pdo->beginTransaction();
        
        // Insert student record
        $stmt = $pdo->prepare("
            INSERT INTO students (name, email, course, rfid_id, fingerprint_template, status) 
            VALUES (?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            $data['name'],
            $data['email'],
            $data['course'],
            $data['rfid_id'],
            $data['fingerprint'] ?? null
        ]);
        
        $student_id = $pdo->lastInsertId();
        
        // Initialize finance record
        $threshold = getSetting($pdo, 'global_exam_threshold', 100.00);
        $stmt = $pdo->prepare("
            INSERT INTO finance (student_id, required_amount, amount_paid, payment_status) 
            VALUES (?, ?, 0.00, 'unpaid')
        ");
        $stmt->execute([$student_id, $threshold]);
        
        $pdo->commit();
        
        // Log registration event
        logSystemEvent($pdo, 'registration', $student_id, [
            'name' => $data['name'],
            'email' => $data['email'],
            'course' => $data['course'],
            'rfid_id' => $data['rfid_id']
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Student registered successfully',
            'student_id' => $student_id,
            'data' => [
                'student_id' => $student_id,
                'name' => $data['name'],
                'email' => $data['email'],
                'course' => $data['course'],
                'rfid_id' => $data['rfid_id'],
                'status' => 'active'
            ]
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        sendResponse([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ], 500);
    } catch (Exception $e) {
        $pdo->rollBack();
        sendResponse([
            'success' => false,
            'message' => 'Registration failed: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get all students with their basic information
 */
function getStudents($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                s.rfid_id,
                s.status,
                s.registration_date,
                f.required_amount,
                f.amount_paid,
                f.payment_status,
                COALESCE(att.total_attendance, 0) as total_attendance,
                COALESCE(att.present_days, 0) as present_days,
                COALESCE(att.absent_days, 0) as absent_days
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            LEFT JOIN (
                SELECT 
                    student_id,
                    COUNT(*) as total_attendance,
                    SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days
                FROM attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY student_id
            ) att ON s.student_id = att.student_id
            ORDER BY s.registration_date DESC
        ");
        $stmt->execute();
        $students = $stmt->fetchAll();
        
        sendResponse([
    'success' => true,
    'data' => $students,         // ← To this
    'count' => count($students)
]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching students: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get single student by ID
 */
function getStudent($pdo, $student_id) {
    try {
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                s.*,
                f.required_amount,
                f.amount_paid,
                f.payment_status,
                f.last_payment_date
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.student_id = ?
        ");
        $stmt->execute([$student_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse(['success' => false, 'message' => 'Student not found'], 404);
        }
        
        // Get recent attendance
        $stmt = $pdo->prepare("
            SELECT * FROM attendance 
            WHERE student_id = ? 
            ORDER BY attendance_date DESC 
            LIMIT 30
        ");
        $stmt->execute([$student_id]);
        $attendance = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'student' => $student,
            'attendance' => $attendance
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching student: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update student information
 */
function updateStudent($pdo, $data) {
    try {
        $student_id = $data['student_id'] ?? '';
        
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        // Check if student exists
        $stmt = $pdo->prepare("SELECT student_id FROM students WHERE student_id = ?");
        $stmt->execute([$student_id]);
        if (!$stmt->fetch()) {
            sendResponse(['success' => false, 'message' => 'Student not found'], 404);
        }
        
        $updateFields = [];
        $updateValues = [];
        
        $allowedFields = ['name', 'email', 'course', 'status'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "$field = ?";
                $updateValues[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            sendResponse(['success' => false, 'message' => 'No valid fields to update'], 400);
        }
        
        $updateValues[] = $student_id;
        
        $stmt = $pdo->prepare("
            UPDATE students 
            SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP 
            WHERE student_id = ?
        ");
        $stmt->execute($updateValues);
        
        logSystemEvent($pdo, 'student_update', $student_id, $data);
        
        sendResponse([
            'success' => true,
            'message' => 'Student updated successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error updating student: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Delete/deactivate student
 */
function deleteStudent($pdo, $student_id) {
    try {
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        // Instead of deleting, deactivate the student
        $stmt = $pdo->prepare("
            UPDATE students 
            SET status = 'inactive', updated_at = CURRENT_TIMESTAMP 
            WHERE student_id = ?
        ");
        $stmt->execute([$student_id]);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(['success' => false, 'message' => 'Student not found'], 404);
        }
        
        logSystemEvent($pdo, 'student_deactivate', $student_id);
        
        sendResponse([
            'success' => true,
            'message' => 'Student deactivated successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error deactivating student: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Search students by name or email
 */
function searchStudents($pdo, $query) {
    try {
        if (empty($query)) {
            sendResponse(['success' => false, 'message' => 'Search query required'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT student_id, name, email, course, rfid_id, status
            FROM students 
            WHERE (name LIKE ? OR email LIKE ?) AND status = 'active'
            ORDER BY name
            LIMIT 20
        ");
        $searchTerm = "%$query%";
        $stmt->execute([$searchTerm, $searchTerm]);
        $results = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'results' => $results,
            'count' => count($results)
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error searching students: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get student by RFID for ESP32 communication
 */
function getStudentByRFID($pdo, $rfid_id) {
    try {
        if (empty($rfid_id)) {
            sendResponse(['success' => false, 'message' => 'RFID ID required'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                s.rfid_id,
                s.status,
                f.required_amount,
                f.amount_paid,
                f.payment_status
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.rfid_id = ? AND s.status = 'active'
        ");
        $stmt->execute([$rfid_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse([
                'success' => false,
                'message' => 'RFID not registered',
                'found' => false
            ], 404);
        }
        
        // Check if already checked in today
        $stmt = $pdo->prepare("
            SELECT check_in_time, check_out_time 
            FROM attendance 
            WHERE student_id = ? AND attendance_date = CURDATE()
        ");
        $stmt->execute([$student['student_id']]);
        $todayAttendance = $stmt->fetch();
        
        sendResponse([
            'success' => true,
            'found' => true,
            'student' => $student,
            'today_attendance' => $todayAttendance,
            'message' => 'Student found'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error finding student: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get student by fingerprint for ESP32 communication
 */
function getStudentByFingerprint($pdo, $fingerprint_id) {
    try {
        if (empty($fingerprint_id)) {
            sendResponse(['success' => false, 'message' => 'Fingerprint ID required'], 400);
        }
        
        // In a real implementation, you would match against stored fingerprint templates
        // For prototype, we'll use a simple mapping
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                s.status,
                f.required_amount,
                f.amount_paid,
                f.payment_status
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.fingerprint_template = ? AND s.status = 'active'
        ");
        $stmt->execute([$fingerprint_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse([
                'success' => false,
                'message' => 'Fingerprint not recognized',
                'found' => false
            ], 404);
        }
        
        // Check exam eligibility
        $eligibility = checkExamEligibility($pdo, $student['student_id']);
        
        sendResponse([
            'success' => true,
            'found' => true,
            'student' => $student,
            'eligibility' => $eligibility,
            'message' => 'Student identified'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error identifying student: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Check exam eligibility for a student
 */
function checkExamEligibility($pdo, $student_id) {
    try {
        // Get absence count in last 30 days
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as absent_count
            FROM attendance 
            WHERE student_id = ? 
            AND status = 'absent' 
            AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
        $stmt->execute([$student_id]);
        $attendance = $stmt->fetch();
        $absent_count = $attendance['absent_count'] ?? 0;
        
        // Get payment status
        $stmt = $pdo->prepare("
            SELECT required_amount, amount_paid 
            FROM finance 
            WHERE student_id = ?
        ");
        $stmt->execute([$student_id]);
        $finance = $stmt->fetch();
        
        $required = $finance['required_amount'] ?? 100;
        $paid = $finance['amount_paid'] ?? 0;
        
        // Check eligibility criteria
        $max_absences = getSetting($pdo, 'max_absences_per_month', 4);
        $attendance_eligible = $absent_count < $max_absences;
        $finance_eligible = $paid >= $required;
        $overall_eligible = $attendance_eligible && $finance_eligible;
        
        return [
            'attendance_eligible' => $attendance_eligible,
            'finance_eligible' => $finance_eligible,
            'overall_eligible' => $overall_eligible,
            'absent_count' => $absent_count,
            'max_absences' => $max_absences,
            'amount_paid' => $paid,
            'required_amount' => $required,
            'reason' => !$overall_eligible ? 
                (!$attendance_eligible ? 'Too many absences' : 'Payment required') : 
                'Eligible'
        ];
        
    } catch (Exception $e) {
        return [
            'attendance_eligible' => false,
            'finance_eligible' => false,
            'overall_eligible' => false,
            'reason' => 'Error checking eligibility'
        ];
    }
}
?>