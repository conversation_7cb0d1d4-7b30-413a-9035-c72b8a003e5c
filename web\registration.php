<?php
$pageTitle = 'Student Registration';
require_once __DIR__ . '/includes/header.php';

// Get registration statistics
try {
    $db = getDB();
    
    // Total registered students
    $stmt = $db->query("SELECT COUNT(*) as total FROM students WHERE status = 'active'");
    $totalStudents = $stmt->fetch()['total'];
    
    // Students registered today
    $stmt = $db->query("SELECT COUNT(*) as today FROM students WHERE DATE(created_at) = CURDATE()");
    $registeredToday = $stmt->fetch()['today'];
    
    // Students with RFID
    $stmt = $db->query("SELECT COUNT(*) as with_rfid FROM students WHERE rfid_uid IS NOT NULL AND status = 'active'");
    $studentsWithRFID = $stmt->fetch()['with_rfid'];
    
    // Students with fingerprint
    $stmt = $db->query("SELECT COUNT(*) as with_fingerprint FROM students WHERE fingerprint_id IS NOT NULL AND status = 'active'");
    $studentsWithFingerprint = $stmt->fetch()['with_fingerprint'];
    
    // Recent registrations
    $stmt = $db->query("
        SELECT name, student_id, phone, email, created_at,
               CASE WHEN rfid_uid IS NOT NULL THEN 'Yes' ELSE 'No' END as has_rfid,
               CASE WHEN fingerprint_id IS NOT NULL THEN 'Yes' ELSE 'No' END as has_fingerprint
        FROM students 
        WHERE status = 'active'
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recentRegistrations = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Registration page error: " . $e->getMessage());
    $totalStudents = $registeredToday = $studentsWithRFID = $studentsWithFingerprint = 0;
    $recentRegistrations = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-plus text-primary me-2"></i>
                        Student Registration
                    </h1>
                    <p class="text-muted mb-0">Register new students with advanced RFID and fingerprint authentication</p>
                </div>
            </div>

            <!-- Registration Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-users text-primary fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Total Students</div>
                                    <div class="fs-4 fw-bold text-primary"><?php echo $totalStudents; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-user-check text-success fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Registered Today</div>
                                    <div class="fs-4 fw-bold text-success"><?php echo $registeredToday; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-credit-card text-info fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">With RFID</div>
                                    <div class="fs-4 fw-bold text-info"><?php echo $studentsWithRFID; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-fingerprint text-warning fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">With Fingerprint</div>
                                    <div class="fs-4 fw-bold text-warning"><?php echo $studentsWithFingerprint; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hardware Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-microchip me-2"></i>
                                Hardware Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center p-3 border rounded">
                                        <div class="mb-2">
                                            <i class="fas fa-credit-card fs-2 text-info" id="rfid-icon"></i>
                                        </div>
                                        <h6>RFID Scanner</h6>
                                        <span class="badge bg-secondary" id="rfid-status">Checking...</span>
                                        <div class="small text-muted mt-1" id="rfid-details">Initializing...</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 border rounded">
                                        <div class="mb-2">
                                            <i class="fas fa-fingerprint fs-2 text-warning" id="fingerprint-icon"></i>
                                        </div>
                                        <h6>Fingerprint Scanner</h6>
                                        <span class="badge bg-secondary" id="fingerprint-status">Checking...</span>
                                        <div class="small text-muted mt-1" id="fingerprint-details">Initializing...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                New Student Registration
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="registrationForm">
                                <div class="mb-3">
                                    <label for="studentName" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="studentName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="studentId" class="form-label">Student ID</label>
                                    <input type="text" class="form-control" id="studentId" required>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="studentPhone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="studentPhone">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="studentEmail" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="studentEmail">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enrollRFID" checked>
                                        <label class="form-check-label" for="enrollRFID">
                                            Enroll RFID Card
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enrollFingerprint" checked>
                                        <label class="form-check-label" for="enrollFingerprint">
                                            Enroll Fingerprint
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary w-100" onclick="startRegistration()" id="registerBtn">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Start Registration
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Registrations -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Registrations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Student ID</th>
                                    <th>Name</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>RFID</th>
                                    <th>Fingerprint</th>
                                    <th>Registered</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentRegistrations as $student): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                    <td><?php echo htmlspecialchars($student['phone']); ?></td>
                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    <td>
                                        <span class="badge <?php echo $student['has_rfid'] == 'Yes' ? 'bg-success' : 'bg-secondary'; ?>">
                                            <?php echo $student['has_rfid']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $student['has_fingerprint'] == 'Yes' ? 'bg-success' : 'bg-secondary'; ?>">
                                            <?php echo $student['has_fingerprint']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M j, Y', strtotime($student['created_at'])); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewStudent('<?php echo $student['student_id']; ?>')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" 
                                                onclick="editStudent('<?php echo $student['student_id']; ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registration Progress Modal -->
<div class="modal fade" id="registrationModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Student Registration Progress</h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-credit-card me-2"></i>
                                RFID Enrollment
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-credit-card fs-1 text-primary" id="modal-rfid-icon"></i>
                                </div>
                                <div class="badge bg-secondary mb-2" id="modal-rfid-status">Waiting...</div>
                                <div class="small text-muted" id="modal-rfid-details">Place RFID card near scanner</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <i class="fas fa-fingerprint me-2"></i>
                                Fingerprint Enrollment
                            </div>
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-fingerprint fs-1 text-warning" id="modal-fingerprint-icon"></i>
                                </div>
                                <div class="badge bg-secondary mb-2" id="modal-fingerprint-status">Waiting...</div>
                                <div class="small text-muted" id="modal-fingerprint-details">Place finger on scanner</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="registrationProgress"></div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted" id="progressText">Starting registration...</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="cancelRegistration()">Cancel</button>
                <button type="button" class="btn btn-success" onclick="completeRegistration()" id="completeBtn" disabled>
                    Complete Registration
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Preserve all original JavaScript functionality from registration.html
let registrationData = {
    name: '',
    studentId: '',
    phone: '',
    email: '',
    rfidUid: null,
    fingerprintId: null,
    enrollRFID: true,
    enrollFingerprint: true
};

let registrationModal;

document.addEventListener('DOMContentLoaded', function() {
    registrationModal = new bootstrap.Modal(document.getElementById('registrationModal'));
    checkHardwareStatus();
    setInterval(checkHardwareStatus, 5000); // Check every 5 seconds
});

async function checkHardwareStatus() {
    try {
        // Check RFID status
        const rfidResponse = await fetch('api/rfid.php?action=status');
        const rfidData = await rfidResponse.json();
        updateRFIDStatus(rfidData);
        
        // Check fingerprint status
        const fingerprintResponse = await fetch('api/fingerprint.php?action=status');
        const fingerprintData = await fingerprintResponse.json();
        updateFingerprintStatus(fingerprintData);
        
    } catch (error) {
        console.error('Error checking hardware status:', error);
        updateRFIDStatus({ connected: false });
        updateFingerprintStatus({ connected: false });
    }
}

function updateRFIDStatus(data) {
    const icon = document.getElementById('rfid-icon');
    const status = document.getElementById('rfid-status');
    const details = document.getElementById('rfid-details');
    
    if (data.connected) {
        icon.className = 'fas fa-credit-card fs-2 text-success';
        status.className = 'badge bg-success';
        status.textContent = 'Connected';
        details.textContent = 'Ready to scan';
    } else {
        icon.className = 'fas fa-credit-card fs-2 text-danger';
        status.className = 'badge bg-danger';
        status.textContent = 'Disconnected';
        details.textContent = 'Check connection';
    }
}

function updateFingerprintStatus(data) {
    const icon = document.getElementById('fingerprint-icon');
    const status = document.getElementById('fingerprint-status');
    const details = document.getElementById('fingerprint-details');
    
    if (data.connected) {
        icon.className = 'fas fa-fingerprint fs-2 text-success';
        status.className = 'badge bg-success';
        status.textContent = 'Connected';
        details.textContent = 'Ready to scan';
    } else {
        icon.className = 'fas fa-fingerprint fs-2 text-danger';
        status.className = 'badge bg-danger';
        status.textContent = 'Disconnected';
        details.textContent = 'Check connection';
    }
}

function startRegistration() {
    // Validate form
    const name = document.getElementById('studentName').value;
    const studentId = document.getElementById('studentId').value;
    const phone = document.getElementById('studentPhone').value;
    const email = document.getElementById('studentEmail').value;
    
    if (!name || !studentId) {
        alert('Please fill in required fields (Name and Student ID)');
        return;
    }
    
    // Store registration data
    registrationData = {
        name: name,
        studentId: studentId,
        phone: phone,
        email: email,
        rfidUid: null,
        fingerprintId: null,
        enrollRFID: document.getElementById('enrollRFID').checked,
        enrollFingerprint: document.getElementById('enrollFingerprint').checked
    };
    
    // Show modal and start enrollment process
    registrationModal.show();
    updateProgress(0, 'Starting registration...');
    
    if (registrationData.enrollRFID) {
        startRFIDEnrollment();
    } else if (registrationData.enrollFingerprint) {
        startFingerprintEnrollment();
    } else {
        // No biometric enrollment needed
        updateProgress(100, 'Ready to complete registration');
        document.getElementById('completeBtn').disabled = false;
    }
}

async function startRFIDEnrollment() {
    updateProgress(25, 'Waiting for RFID card...');
    
    try {
        const response = await fetch('api/rfid.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'enroll',
                student_id: registrationData.studentId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            registrationData.rfidUid = data.rfid_uid;
            updateProgress(50, 'RFID enrolled successfully');
            
            if (registrationData.enrollFingerprint) {
                startFingerprintEnrollment();
            } else {
                updateProgress(100, 'Ready to complete registration');
                document.getElementById('completeBtn').disabled = false;
            }
        } else {
            alert('RFID enrollment failed: ' + data.message);
        }
    } catch (error) {
        console.error('Error enrolling RFID:', error);
        alert('RFID enrollment failed');
    }
}

async function startFingerprintEnrollment() {
    updateProgress(registrationData.enrollRFID ? 50 : 25, 'Waiting for fingerprint...');
    
    try {
        const response = await fetch('api/fingerprint.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'enroll',
                student_id: registrationData.studentId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            registrationData.fingerprintId = data.fingerprint_id;
            updateProgress(100, 'Fingerprint enrolled successfully');
            document.getElementById('completeBtn').disabled = false;
        } else {
            alert('Fingerprint enrollment failed: ' + data.message);
        }
    } catch (error) {
        console.error('Error enrolling fingerprint:', error);
        alert('Fingerprint enrollment failed');
    }
}

async function completeRegistration() {
    try {
        const response = await fetch('api/students.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'register',
                name: registrationData.name,
                student_id: registrationData.studentId,
                phone: registrationData.phone,
                email: registrationData.email,
                rfid_uid: registrationData.rfidUid,
                fingerprint_id: registrationData.fingerprintId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Student registered successfully!');
            registrationModal.hide();
            location.reload();
        } else {
            alert('Registration failed: ' + data.message);
        }
    } catch (error) {
        console.error('Error completing registration:', error);
        alert('Registration failed');
    }
}

function cancelRegistration() {
    registrationModal.hide();
    // Reset form
    document.getElementById('registrationForm').reset();
}

function updateProgress(percentage, text) {
    document.getElementById('registrationProgress').style.width = percentage + '%';
    document.getElementById('progressText').textContent = text;
}

function viewStudent(studentId) {
    window.location.href = `students/index.php?id=${studentId}`;
}

function editStudent(studentId) {
    window.location.href = `students/index.php?action=edit&id=${studentId}`;
}
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
