<?php
$pageTitle = 'System Settings';
require_once __DIR__ . '/includes/header.php';

// Get system statistics and settings
try {
    $db = getDB();
    
    // System uptime (approximate based on oldest log entry)
    $stmt = $db->query("SELECT MIN(created_at) as oldest_log FROM audit_logs");
    $oldestLog = $stmt->fetch()['oldest_log'];
    $uptime = $oldestLog ? time() - strtotime($oldestLog) : 0;
    $uptimeFormatted = formatUptime($uptime);
    
    // Database status
    $databaseStatus = 'Connected';
    
    // Total records
    $stmt = $db->query("
        SELECT 
            (SELECT COUNT(*) FROM students) as students,
            (SELECT COUNT(*) FROM attendance) as attendance,
            (SELECT COUNT(*) FROM payments) as payments
    ");
    $recordCounts = $stmt->fetch();
    $totalRecords = $recordCounts['students'] + $recordCounts['attendance'] + $recordCounts['payments'];
    
    // System settings
    $stmt = $db->query("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $systemSettings = $stmt->fetchAll();
    
    // Recent system logs
    $stmt = $db->query("
        SELECT action, user_id, details, created_at 
        FROM audit_logs 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $systemLogs = $stmt->fetchAll();
    
    // Hardware status (simulated)
    $hardwareStatus = [
        'esp32' => ['status' => 'Connected', 'last_seen' => date('Y-m-d H:i:s')],
        'rfid' => ['status' => 'Active', 'last_scan' => date('Y-m-d H:i:s', strtotime('-5 minutes'))],
        'fingerprint' => ['status' => 'Active', 'last_scan' => date('Y-m-d H:i:s', strtotime('-2 minutes'))]
    ];
    
} catch (Exception $e) {
    error_log("System page error: " . $e->getMessage());
    $uptimeFormatted = '0h 0m';
    $databaseStatus = 'Error';
    $totalRecords = 0;
    $systemSettings = $systemLogs = [];
    $hardwareStatus = [];
}

function formatUptime($seconds) {
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    
    if ($days > 0) {
        return "{$days}d {$hours}h {$minutes}m";
    } elseif ($hours > 0) {
        return "{$hours}h {$minutes}m";
    } else {
        return "{$minutes}m";
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        System Settings
                    </h1>
                    <p class="text-muted mb-0">Configure and monitor your attendance management system</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>
                        Export Settings
                    </button>
                    <button class="btn btn-primary" onclick="backupSystem()">
                        <i class="fas fa-save me-2"></i>
                        Backup System
                    </button>
                </div>
            </div>

            <!-- System Status Overview -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-clock text-success fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">System Uptime</div>
                                    <div class="fs-4 fw-bold text-success"><?php echo $uptimeFormatted; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-database text-primary fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Database Status</div>
                                    <div class="fs-4 fw-bold text-primary"><?php echo $databaseStatus; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-chart-bar text-info fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Total Records</div>
                                    <div class="fs-4 fw-bold text-info"><?php echo number_format($totalRecords); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-microchip text-warning fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Hardware Status</div>
                                    <div class="fs-4 fw-bold text-warning">Online</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <ul class="nav nav-tabs card-header-tabs" id="systemTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" 
                                            data-bs-target="#settings" type="button" role="tab">
                                        <i class="fas fa-cog me-2"></i>
                                        Settings
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="hardware-tab" data-bs-toggle="tab" 
                                            data-bs-target="#hardware" type="button" role="tab">
                                        <i class="fas fa-microchip me-2"></i>
                                        Hardware
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="logs-tab" data-bs-toggle="tab" 
                                            data-bs-target="#logs" type="button" role="tab">
                                        <i class="fas fa-list me-2"></i>
                                        System Logs
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" 
                                            data-bs-target="#maintenance" type="button" role="tab">
                                        <i class="fas fa-tools me-2"></i>
                                        Maintenance
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="systemTabContent">
                                <!-- Settings Tab -->
                                <div class="tab-pane fade show active" id="settings" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6>System Configuration</h6>
                                            <form id="settingsForm">
                                                <?php foreach ($systemSettings as $setting): ?>
                                                <div class="mb-3">
                                                    <label for="setting_<?php echo $setting['setting_key']; ?>" class="form-label">
                                                        <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                                    </label>
                                                    <input type="text" class="form-control" 
                                                           id="setting_<?php echo $setting['setting_key']; ?>"
                                                           name="<?php echo $setting['setting_key']; ?>"
                                                           value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                                           data-description="<?php echo htmlspecialchars($setting['description']); ?>">
                                                    <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                                </div>
                                                <?php endforeach; ?>
                                                <button type="button" class="btn btn-primary" onclick="saveSettings()">
                                                    <i class="fas fa-save me-2"></i>
                                                    Save Settings
                                                </button>
                                            </form>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Quick Actions</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-primary" onclick="testEmailSettings()">
                                                    <i class="fas fa-envelope me-2"></i>
                                                    Test Email Settings
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="clearCache()">
                                                    <i class="fas fa-trash me-2"></i>
                                                    Clear Cache
                                                </button>
                                                <button class="btn btn-outline-info" onclick="optimizeDatabase()">
                                                    <i class="fas fa-database me-2"></i>
                                                    Optimize Database
                                                </button>
                                                <button class="btn btn-outline-success" onclick="restartServices()">
                                                    <i class="fas fa-redo me-2"></i>
                                                    Restart Services
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hardware Tab -->
                                <div class="tab-pane fade" id="hardware" role="tabpanel">
                                    <div class="row">
                                        <?php foreach ($hardwareStatus as $device => $status): ?>
                                        <div class="col-md-4">
                                            <div class="card border-0 bg-light">
                                                <div class="card-body text-center">
                                                    <div class="mb-3">
                                                        <i class="fas fa-<?php echo $device == 'esp32' ? 'microchip' : ($device == 'rfid' ? 'credit-card' : 'fingerprint'); ?> fs-2 text-primary"></i>
                                                    </div>
                                                    <h6><?php echo strtoupper($device); ?></h6>
                                                    <span class="badge bg-success"><?php echo $status['status']; ?></span>
                                                    <div class="small text-muted mt-2">
                                                        Last seen: <?php echo date('H:i:s', strtotime($status['last_seen'] ?? $status['last_scan'])); ?>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-primary mt-2" 
                                                            onclick="testDevice('<?php echo $device; ?>')">
                                                        Test Device
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>

                                <!-- System Logs Tab -->
                                <div class="tab-pane fade" id="logs" role="tabpanel">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6>Recent System Activity</h6>
                                        <button class="btn btn-sm btn-outline-danger" onclick="clearLogs()">
                                            <i class="fas fa-trash me-2"></i>
                                            Clear Logs
                                        </button>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Time</th>
                                                    <th>Action</th>
                                                    <th>User</th>
                                                    <th>Details</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($systemLogs as $log): ?>
                                                <tr>
                                                    <td><?php echo date('M j, H:i', strtotime($log['created_at'])); ?></td>
                                                    <td><?php echo htmlspecialchars($log['action']); ?></td>
                                                    <td><?php echo htmlspecialchars($log['user_id'] ?? 'System'); ?></td>
                                                    <td><?php echo htmlspecialchars($log['details'] ?? ''); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Maintenance Tab -->
                                <div class="tab-pane fade" id="maintenance" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Database Maintenance</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-primary" onclick="backupDatabase()">
                                                    <i class="fas fa-download me-2"></i>
                                                    Backup Database
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                                    <i class="fas fa-cogs me-2"></i>
                                                    Optimize Tables
                                                </button>
                                                <button class="btn btn-outline-info" onclick="checkDatabaseIntegrity()">
                                                    <i class="fas fa-check me-2"></i>
                                                    Check Integrity
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>System Maintenance</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-success" onclick="updateSystem()">
                                                    <i class="fas fa-sync me-2"></i>
                                                    Check Updates
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="clearLogs()">
                                                    <i class="fas fa-trash me-2"></i>
                                                    Clear System Logs
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="resetSystem()">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    Reset System
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Preserve all original JavaScript functionality from system.html
async function saveSettings() {
    const formData = new FormData(document.getElementById('settingsForm'));
    const settings = {};
    
    for (let [key, value] of formData.entries()) {
        settings[key] = value;
    }
    
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'save_settings',
                settings: settings
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Settings saved successfully');
        } else {
            alert(data.message || 'Failed to save settings');
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        alert('Failed to save settings');
    }
}

async function testEmailSettings() {
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'test_email'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Email test successful');
        } else {
            alert('Email test failed: ' + data.message);
        }
    } catch (error) {
        console.error('Error testing email:', error);
        alert('Email test failed');
    }
}

async function clearCache() {
    if (!confirm('Clear system cache? This may temporarily slow down the system.')) return;
    
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'clear_cache'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Cache cleared successfully');
        } else {
            alert('Failed to clear cache: ' + data.message);
        }
    } catch (error) {
        console.error('Error clearing cache:', error);
        alert('Failed to clear cache');
    }
}

async function optimizeDatabase() {
    if (!confirm('Optimize database? This may take a few minutes.')) return;
    
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'optimize_database'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Database optimized successfully');
        } else {
            alert('Failed to optimize database: ' + data.message);
        }
    } catch (error) {
        console.error('Error optimizing database:', error);
        alert('Failed to optimize database');
    }
}

async function testDevice(device) {
    try {
        const response = await fetch(`api/${device}.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'test'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert(`${device.toUpperCase()} test successful`);
        } else {
            alert(`${device.toUpperCase()} test failed: ` + data.message);
        }
    } catch (error) {
        console.error(`Error testing ${device}:`, error);
        alert(`${device.toUpperCase()} test failed`);
    }
}

async function backupDatabase() {
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'backup_database'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Database backup created successfully');
            if (data.download_url) {
                window.open(data.download_url, '_blank');
            }
        } else {
            alert('Failed to backup database: ' + data.message);
        }
    } catch (error) {
        console.error('Error backing up database:', error);
        alert('Failed to backup database');
    }
}

function backupSystem() {
    if (confirm('Create a full system backup? This may take several minutes.')) {
        window.open('api/system.php?action=full_backup', '_blank');
    }
}

function exportSettings() {
    window.open('api/system.php?action=export_settings', '_blank');
}

async function clearLogs() {
    if (!confirm('Clear all system logs? This action cannot be undone.')) return;
    
    try {
        const response = await fetch('api/system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'clear_logs'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('System logs cleared');
            location.reload();
        } else {
            alert('Failed to clear logs: ' + data.message);
        }
    } catch (error) {
        console.error('Error clearing logs:', error);
        alert('Failed to clear logs');
    }
}

function restartServices() {
    if (confirm('Restart system services? This may cause temporary downtime.')) {
        alert('Service restart initiated. Please wait...');
        // Implementation would depend on server setup
    }
}

function updateSystem() {
    alert('System update check feature coming soon');
}

function checkDatabaseIntegrity() {
    alert('Database integrity check feature coming soon');
}

function resetSystem() {
    if (confirm('Reset system to default settings? This will remove all custom configurations.')) {
        if (confirm('Are you absolutely sure? This action cannot be undone.')) {
            alert('System reset feature coming soon');
        }
    }
}

// Auto-refresh system status every 30 seconds
setInterval(function() {
    location.reload();
}, 30000);
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
