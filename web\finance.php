<?php
$pageTitle = 'Finance Management';
require_once __DIR__ . '/includes/header.php';

// Get finance statistics
try {
    $db = getDB();
    
    // Total revenue this month
    $stmt = $db->query("
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM payments 
        WHERE MONTH(payment_date) = MONTH(CURDATE()) 
        AND YEAR(payment_date) = YEAR(CURDATE())
    ");
    $totalRevenue = $stmt->fetch()['total'];
    
    // Pending payments
    $stmt = $db->query("
        SELECT COUNT(*) as pending 
        FROM students s 
        LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) 
        WHERE s.status = 'active' AND p.id IS NULL
    ");
    $pendingPayments = $stmt->fetch()['pending'];
    
    // Paid students this month
    $stmt = $db->query("
        SELECT COUNT(DISTINCT p.student_id) as paid 
        FROM payments p 
        WHERE MONTH(p.payment_date) = MONTH(CURDATE()) 
        AND YEAR(p.payment_date) = YEAR(CURDATE())
    ");
    $paidStudents = $stmt->fetch()['paid'];
    
    // Collection rate
    $stmt = $db->query("SELECT COUNT(*) as total FROM students WHERE status = 'active'");
    $totalStudents = $stmt->fetch()['total'];
    $collectionRate = $totalStudents > 0 ? round(($paidStudents / $totalStudents) * 100) : 0;
    
    // Recent payments
    $stmt = $db->query("
        SELECT s.name, s.student_id, p.amount, p.payment_date, p.payment_type, p.reference_number
        FROM payments p 
        JOIN students s ON p.student_id = s.id 
        ORDER BY p.payment_date DESC 
        LIMIT 20
    ");
    $recentPayments = $stmt->fetchAll();
    
    // Outstanding payments
    $stmt = $db->query("
        SELECT s.name, s.student_id, s.phone, s.email
        FROM students s 
        LEFT JOIN payments p ON s.id = p.student_id AND MONTH(p.payment_date) = MONTH(CURDATE()) 
        WHERE s.status = 'active' AND p.id IS NULL
        ORDER BY s.name
    ");
    $outstandingPayments = $stmt->fetchAll();
    
    // Monthly revenue trend (last 6 months)
    $stmt = $db->query("
        SELECT 
            DATE_FORMAT(payment_date, '%Y-%m') as month,
            SUM(amount) as total
        FROM payments 
        WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
        ORDER BY month
    ");
    $monthlyTrend = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Finance page error: " . $e->getMessage());
    $totalRevenue = $pendingPayments = $paidStudents = $totalStudents = $collectionRate = 0;
    $recentPayments = $outstandingPayments = $monthlyTrend = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-credit-card text-primary me-2"></i>
                        Finance Management
                    </h1>
                    <p class="text-muted mb-0">Manage student payments and exam fee requirements</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                        <i class="fas fa-plus me-2"></i>
                        Add Payment
                    </button>
                </div>
            </div>

            <!-- Finance Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-dollar-sign text-success fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Total Revenue</div>
                                    <div class="fs-4 fw-bold text-success">$<?php echo number_format($totalRevenue, 2); ?></div>
                                    <div class="text-muted small">This month</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-clock text-warning fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Pending Payments</div>
                                    <div class="fs-4 fw-bold text-warning"><?php echo $pendingPayments; ?></div>
                                    <div class="text-muted small">Students</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-check-circle text-primary fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Paid Students</div>
                                    <div class="fs-4 fw-bold text-primary"><?php echo $paidStudents; ?></div>
                                    <div class="text-muted small">This month</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                        <i class="fas fa-percentage text-info fs-4"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="text-muted small">Collection Rate</div>
                                    <div class="fs-4 fw-bold text-info"><?php echo $collectionRate; ?>%</div>
                                    <div class="text-muted small">This month</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-transparent border-0">
                            <ul class="nav nav-tabs card-header-tabs" id="financeTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="recent-tab" data-bs-toggle="tab" 
                                            data-bs-target="#recent" type="button" role="tab">
                                        <i class="fas fa-history me-2"></i>
                                        Recent Payments
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="outstanding-tab" data-bs-toggle="tab" 
                                            data-bs-target="#outstanding" type="button" role="tab">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Outstanding Payments
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" 
                                            data-bs-target="#reports" type="button" role="tab">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Reports
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="financeTabContent">
                                <!-- Recent Payments Tab -->
                                <div class="tab-pane fade show active" id="recent" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Student ID</th>
                                                    <th>Name</th>
                                                    <th>Amount</th>
                                                    <th>Payment Date</th>
                                                    <th>Type</th>
                                                    <th>Reference</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentPayments as $payment): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($payment['student_id']); ?></td>
                                                    <td><?php echo htmlspecialchars($payment['name']); ?></td>
                                                    <td class="fw-bold text-success">$<?php echo number_format($payment['amount'], 2); ?></td>
                                                    <td><?php echo date('M j, Y', strtotime($payment['payment_date'])); ?></td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?php echo htmlspecialchars($payment['payment_type']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($payment['reference_number']); ?></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary" 
                                                                onclick="viewPaymentDetails('<?php echo $payment['reference_number']; ?>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Outstanding Payments Tab -->
                                <div class="tab-pane fade" id="outstanding" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Student ID</th>
                                                    <th>Name</th>
                                                    <th>Phone</th>
                                                    <th>Email</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($outstandingPayments as $student): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['phone']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-success me-1" 
                                                                onclick="recordPayment('<?php echo $student['student_id']; ?>')">
                                                            <i class="fas fa-plus"></i> Record Payment
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning" 
                                                                onclick="sendReminder('<?php echo $student['student_id']; ?>')">
                                                            <i class="fas fa-bell"></i> Send Reminder
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Reports Tab -->
                                <div class="tab-pane fade" id="reports" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6>Monthly Revenue Trend</h6>
                                            <canvas id="revenueChart" height="100"></canvas>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Quick Reports</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-primary" onclick="generateMonthlyReport()">
                                                    <i class="fas fa-file-pdf me-2"></i>
                                                    Monthly Report
                                                </button>
                                                <button class="btn btn-outline-primary" onclick="generateOutstandingReport()">
                                                    <i class="fas fa-file-excel me-2"></i>
                                                    Outstanding Payments
                                                </button>
                                                <button class="btn btn-outline-primary" onclick="generateCollectionReport()">
                                                    <i class="fas fa-chart-bar me-2"></i>
                                                    Collection Analysis
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPaymentForm">
                    <div class="mb-3">
                        <label for="studentSelect" class="form-label">Student</label>
                        <select class="form-select" id="studentSelect" required>
                            <option value="">Select Student</option>
                            <?php foreach ($outstandingPayments as $student): ?>
                            <option value="<?php echo $student['student_id']; ?>">
                                <?php echo htmlspecialchars($student['student_id'] . ' - ' . $student['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="paymentAmount" class="form-label">Amount</label>
                        <input type="number" class="form-control" id="paymentAmount" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="paymentType" class="form-label">Payment Type</label>
                        <select class="form-select" id="paymentType" required>
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="card">Card</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="referenceNumber" class="form-label">Reference Number</label>
                        <input type="text" class="form-control" id="referenceNumber">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitPayment()">Add Payment</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Preserve all original JavaScript functionality from finance.html
async function submitPayment() {
    const studentId = document.getElementById('studentSelect').value;
    const amount = document.getElementById('paymentAmount').value;
    const paymentType = document.getElementById('paymentType').value;
    const referenceNumber = document.getElementById('referenceNumber').value;
    
    if (!studentId || !amount || !paymentType) {
        alert('Please fill in all required fields');
        return;
    }
    
    try {
        const response = await fetch('api/finance.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add_payment',
                student_id: studentId,
                amount: amount,
                payment_type: paymentType,
                reference_number: referenceNumber
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Payment added successfully');
            location.reload();
        } else {
            alert(data.message || 'Failed to add payment');
        }
    } catch (error) {
        console.error('Error adding payment:', error);
        alert('Failed to add payment');
    }
}

function recordPayment(studentId) {
    document.getElementById('studentSelect').value = studentId;
    const modal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
    modal.show();
}

async function sendReminder(studentId) {
    try {
        const response = await fetch('api/finance.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'send_reminder',
                student_id: studentId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert('Reminder sent successfully');
        } else {
            alert(data.message || 'Failed to send reminder');
        }
    } catch (error) {
        console.error('Error sending reminder:', error);
        alert('Failed to send reminder');
    }
}

function viewPaymentDetails(referenceNumber) {
    // Implementation for viewing payment details
    alert('Payment details for: ' + referenceNumber);
}

function generateMonthlyReport() {
    window.open('api/finance.php?action=monthly_report&format=pdf', '_blank');
}

function generateOutstandingReport() {
    window.open('api/finance.php?action=outstanding_report&format=excel', '_blank');
}

function generateCollectionReport() {
    window.open('api/finance.php?action=collection_report&format=pdf', '_blank');
}

// Initialize revenue chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const monthlyData = <?php echo json_encode($monthlyTrend); ?>;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'Revenue',
                data: monthlyData.map(item => item.total),
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
