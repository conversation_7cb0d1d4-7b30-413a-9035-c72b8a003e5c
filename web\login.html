<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Advanced Attendance System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --success: #10b981;
            --error: #ef4444;
            --warning: #f59e0b;
            --dark: #1e293b;
            --light: #f8fafc;
            --white: #ffffff;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Background Image with Overlay */
        .background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.8) 100%),
                        url('11111.png') center/cover;
            z-index: -1;
        }

        /* Animated Background Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            pointer-events: none;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) { width: 20px; height: 20px; top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 15px; height: 15px; top: 60%; left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { width: 25px; height: 25px; top: 40%; left: 70%; animation-delay: 2s; }
        .particle:nth-child(4) { width: 18px; height: 18px; top: 80%; left: 80%; animation-delay: 3s; }
        .particle:nth-child(5) { width: 22px; height: 22px; top: 30%; left: 85%; animation-delay: 4s; }
        .particle:nth-child(6) { width: 16px; height: 16px; top: 70%; left: 10%; animation-delay: 5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Login Container */
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem 2.5rem;
            width: 100%;
            max-width: 450px;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        /* Logo and Header */
        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 20px;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-lg);
        }

        .logo i {
            font-size: 2rem;
            color: white;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        /* Form Styles */
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .form-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
            margin-top: 12px;
        }

        /* Remember Me & Forgot Password */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.5rem 0;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
        }

        .checkbox-group label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.875rem;
            cursor: pointer;
        }

        .forgot-password {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.875rem;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: white;
            text-decoration: underline;
        }

        /* Login Button */
        .login-btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            margin-top: 1rem;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(99, 102, 241, 0.4);
        }

        .login-btn:hover::before {
            width: 200px;
            height: 200px;
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .login-btn.loading {
            color: transparent;
        }

        .login-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Error Message */
        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.5);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            display: none;
            backdrop-filter: blur(10px);
        }

        .error-message.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }

        /* Footer */
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-footer p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.875rem;
        }

        .login-footer a {
            color: white;
            text-decoration: none;
            font-weight: 600;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.6s ease;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }

            .login-title {
                font-size: 1.75rem;
            }

            .form-options {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }

        /* Success Message */
        .success-message {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.5);
            color: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            display: none;
            backdrop-filter: blur(10px);
        }

        .success-message.show {
            display: block;
            animation: fadeInUp 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Background with Particles -->
    <div class="background"></div>
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-fingerprint"></i>
            </div>
            <h1 class="login-title">Attendance System</h1>
            <p class="login-subtitle">Advanced RFID & Fingerprint Management</p>
        </div>

        <!-- Error/Success Messages -->
        <div id="error-message" class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <span id="error-text">Invalid credentials. Please try again.</span>
        </div>

        <div id="success-message" class="success-message">
            <i class="fas fa-check-circle"></i>
            <span id="success-text">Login successful! Redirecting...</span>
        </div>

        <!-- Login Form -->
        <form id="login-form" class="login-form">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user"></i> Username
                </label>
                <div style="position: relative;">
                    <i class="fas fa-user input-icon"></i>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="Enter your username" 
                        required
                        autocomplete="username"
                    >
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                <div style="position: relative;">
                    <i class="fas fa-lock input-icon"></i>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="Enter your password" 
                        required
                        autocomplete="current-password"
                    >
                </div>
            </div>

            <div class="form-options">
                <div class="checkbox-group">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">Remember me</label>
                </div>
                <a href="#" class="forgot-password" onclick="handleForgotPassword()">
                    Forgot password?
                </a>
            </div>

            <button type="submit" id="login-btn" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                Sign In
            </button>
        </form>

        <!-- Footer -->
        <div class="login-footer">
            <p>
                © 2024 Advanced Attendance System. 
                <a href="#" onclick="showAbout()">About</a> | 
                <a href="#" onclick="showHelp()">Help</a>
            </p>
        </div>
    </div>

    <script>
        // Login form handling
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // Handle login submission
        async function handleLogin() {
            const loginBtn = document.getElementById('login-btn');
            const errorMsg = document.getElementById('error-message');
            const successMsg = document.getElementById('success-message');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            // Hide previous messages
            errorMsg.classList.remove('show');
            successMsg.classList.remove('show');

            // Validate inputs
            if (!username || !password) {
                showError('Please fill in all fields');
                return;
            }

            // Show loading state
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;

            try {
                // Simulate API call (replace with your actual authentication endpoint)
                const response = await fetch('api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        username: username,
                        password: password,
                        remember: remember
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Success
                    showSuccess('Login successful! Redirecting...');
                    
                    // Store session info if needed
                    if (remember) {
                        localStorage.setItem('attendance_user', JSON.stringify({
                            username: username,
                            token: result.token || 'demo_token'
                        }));
                    }

                    // Redirect after delay
                    setTimeout(() => {
                        window.location.href = 'dashboard.php';
                    }, 1500);

                } else {
                    // Error
                    showError(result.message || 'Login failed. Please check your credentials.');
                }

            } catch (error) {
                console.error('Login error:', error);
                
                // For demo purposes, allow admin/admin login
                if (username === 'admin' && password === 'admin') {
                    showSuccess('Demo login successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    showError('Connection error. Please try again.');
                }
            } finally {
                // Remove loading state
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            }
        }

        // Show error message
        function showError(message) {
            const errorMsg = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            errorText.textContent = message;
            errorMsg.classList.add('show');
        }

        // Show success message
        function showSuccess(message) {
            const successMsg = document.getElementById('success-message');
            const successText = document.getElementById('success-text');
            successText.textContent = message;
            successMsg.classList.add('show');
        }

        // Handle forgot password
        function handleForgotPassword() {
            alert('Forgot password functionality would be implemented here.\n\nFor demo purposes:\nUsername: admin\nPassword: admin');
        }

        // Show about dialog
        function showAbout() {
            alert('Advanced Attendance Management System v2.5\n\nFeatures:\n- RFID Card Authentication\n- Fingerprint Scanning\n- Real-time Monitoring\n- Exam Eligibility Control\n- Email Notifications');
        }

        // Show help dialog
        function showHelp() {
            alert('Need help?\n\nDemo Login:\nUsername: admin\nPassword: admin\n\nFor technical support, contact your system administrator.');
        }

        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Enter key handling
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'username') {
                    document.getElementById('password').focus();
                } else if (activeElement.id === 'password') {
                    handleLogin();
                }
            }
        });

        // Check for existing session
        document.addEventListener('DOMContentLoaded', function() {
            const savedUser = localStorage.getItem('attendance_user');
            if (savedUser) {
                try {
                    const user = JSON.parse(savedUser);
                    document.getElementById('username').value = user.username;
                    document.getElementById('remember').checked = true;
                } catch (error) {
                    // Ignore parsing errors
                }
            }
        });

        // Add floating animation to particles
        function animateParticles() {
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                const delay = index * 1000; // 1 second delay between each
                const duration = 6000 + (index * 500); // Varying duration
                
                particle.style.animationDelay = delay + 'ms';
                particle.style.animationDuration = duration + 'ms';
            });
        }

        // Initialize animations
        document.addEventListener('DOMContentLoaded', animateParticles);
    </script>
</body>
</html>